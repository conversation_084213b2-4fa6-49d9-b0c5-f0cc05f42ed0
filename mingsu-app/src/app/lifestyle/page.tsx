'use client';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { motion, AnimatePresence } from 'framer-motion';
import '../lib/fontawesome';

const lifestyleData = {
  peaceful: {
    title: '慢生活体验者',
    subtitle: '寻求内心平静的你，值得拥有这些美好体验',
    color: 'from-blue-400 to-cyan-300',
    icon: 'spa',
    experiences: [
      {
        id: 1,
        title: '山间茶舍，学习茶道冥想',
        location: '杭州西湖',
        image: '🍵',
        icon: 'leaf',
        description: '在云雾缭绕的山间，跟随茶艺师学习正宗茶道，在茶香中找到内心的宁静',
        price: '¥680/人',
        duration: '2天1夜',
        highlights: ['茶道体验', '冥想指导', '山景住宿']
      },
      {
        id: 2,
        title: '海边瑜伽，日出时的宁静',
        location: '三亚海棠湾',
        image: '🧘‍♀️',
        icon: 'spa',
        description: '在海浪声中醒来，跟随瑜伽导师在沙滩上迎接第一缕阳光',
        price: '¥880/人',
        duration: '3天2夜',
        highlights: ['海边瑜伽', '日出冥想', '海景别墅']
      },
      {
        id: 3,
        title: '乡村手工，制作属于你的陶器',
        location: '景德镇古窑',
        image: '🏺',
        icon: 'palette',
        description: '用双手感受泥土的温度，在陶艺师的指导下创作独一无二的作品',
        price: '¥580/人',
        duration: '2天1夜',
        highlights: ['陶艺制作', '古窑参观', '乡村民宿']
      }
    ]
  },
  creative: {
    title: '文艺青年',
    subtitle: '释放创作灵感，在艺术中找到真实的自己',
    color: 'from-purple-400 to-pink-300',
    icon: 'palette',
    experiences: [
      {
        id: 4,
        title: '书店民宿，与作家深度对话',
        location: '北京胡同',
        image: '📚',
        icon: 'book',
        description: '住在书香四溢的独立书店里，与驻店作家畅谈文学与人生',
        price: '¥780/人',
        duration: '2天1夜',
        highlights: ['文学沙龙', '作家对话', '胡同文化']
      },
      {
        id: 5,
        title: '艺术工作室，创作你的第一幅画',
        location: '大理古城',
        image: '🎨',
        icon: 'paint-brush',
        description: '在苍山洱海的灵感中，跟随艺术家学习绘画技巧，创作属于你的作品',
        price: '¥980/人',
        duration: '3天2夜',
        highlights: ['绘画教学', '艺术创作', '古城漫步']
      },
      {
        id: 6,
        title: '咖啡烘焙，学习拉花艺术',
        location: '上海法租界',
        image: '☕',
        icon: 'coffee',
        description: '在百年咖啡馆里学习从烘焙到拉花的全套技艺，品味生活的精致',
        price: '¥680/人',
        duration: '1天',
        highlights: ['咖啡烘焙', '拉花技艺', '法租界文化']
      }
    ]
  },
  adventure: {
    title: '探险家模式',
    subtitle: '挑战自我极限，在冒险中发现更强大的自己',
    color: 'from-orange-400 to-red-300',
    icon: 'mountain',
    experiences: [
      {
        id: 7,
        title: '徒步穿越，征服神秘峡谷',
        location: '张家界天门山',
        image: '🥾',
        icon: 'walking',
        description: '跟随专业向导穿越原始峡谷，在挑战中突破自我界限',
        price: '¥1280/人',
        duration: '3天2夜',
        highlights: ['峡谷穿越', '野外生存', '山顶露营']
      },
      {
        id: 8,
        title: '攀岩体验，挑战垂直极限',
        location: '阳朔遇龙河',
        image: '🧗‍♂️',
        icon: 'mountain',
        description: '在喀斯特地貌的天然岩壁上挑战自我，体验征服高峰的成就感',
        price: '¥980/人',
        duration: '2天1夜',
        highlights: ['攀岩教学', '安全保障', '山水美景']
      }
    ]
  },
  nature: {
    title: '自然探索者',
    subtitle: '回归自然怀抱，与大自然建立深度连接',
    color: 'from-green-400 to-emerald-300',
    icon: 'leaf',
    experiences: [
      {
        id: 9,
        title: '森林木屋，与鸟儿共眠',
        location: '长白山原始森林',
        image: '🌲',
        icon: 'tree',
        description: '住在树梢上的木屋里，听着鸟鸣入睡，在自然中找到内心的平静',
        price: '¥880/人',
        duration: '2天1夜',
        highlights: ['树屋住宿', '森林徒步', '野生动物观察']
      },
      {
        id: 10,
        title: '星空露营，与宇宙对话',
        location: '内蒙古草原',
        image: '⭐',
        icon: 'star',
        description: '在无光污染的草原上仰望星空，感受宇宙的浩瀚与神秘',
        price: '¥680/人',
        duration: '2天1夜',
        highlights: ['星空观测', '草原骑马', '篝火晚会']
      }
    ]
  },
  connection: {
    title: '温暖连接者',
    subtitle: '寻找温暖陪伴，在真挚的人际关系中感受爱与被爱',
    color: 'from-pink-400 to-rose-300',
    icon: 'heart',
    experiences: [
      {
        id: 11,
        title: '家庭农场，体验田园生活',
        location: '成都郊外',
        image: '🏡',
        icon: 'home',
        description: '与热情的农场主一家共同生活，体验最纯朴的人情味',
        price: '¥480/人',
        duration: '2天1夜',
        highlights: ['农场体验', '家庭聚餐', '乡村生活']
      },
      {
        id: 12,
        title: '温泉民宿，治愈系温暖',
        location: '日式温泉小镇',
        image: '♨️',
        icon: 'spa',
        description: '在温泉的温暖中放松身心，与民宿主人分享生活的美好',
        price: '¥780/人',
        duration: '2天1夜',
        highlights: ['私汤温泉', '和式住宿', '温暖服务']
      }
    ]
  }
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      delayChildren: 0.2,
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  }
};

const cardVariants = {
  hidden: { scale: 0.9, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15
    }
  },
  hover: {
    scale: 1.03,
    y: -5,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

export default function LifestylePage() {
  const searchParams = useSearchParams();
  const mood = searchParams.get('mood') || 'peaceful';
  const [selectedExperience, setSelectedExperience] = useState<number | null>(null);
  
  const currentLifestyle = lifestyleData[mood as keyof typeof lifestyleData];

  if (!currentLifestyle) {
    return <div>页面未找到</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* 头部导航 */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-indigo-600 flex items-center">
              <FontAwesomeIcon icon="home" className="mr-2" />
              民宿 ✨
            </Link>
            <div className="flex items-center gap-4">
              <motion.button 
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors flex items-center"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FontAwesomeIcon icon="robot" className="mr-1" />
                小宿
              </motion.button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主内容区域 */}
      <motion.div 
        className="max-w-7xl mx-auto px-6 py-12"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* 标题区域 */}
        <motion.div className="text-center mb-12" variants={itemVariants}>
          <motion.div 
            className={`inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r ${currentLifestyle.color} text-white font-semibold text-lg mb-4`}
            whileHover={{ scale: 1.05 }}
          >
            <FontAwesomeIcon icon={currentLifestyle.icon as any} className="mr-2" />
            {currentLifestyle.title}
          </motion.div>
          <motion.h1 
            className="text-3xl md:text-4xl font-bold text-gray-800 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {currentLifestyle.subtitle}
          </motion.h1>
          <motion.p 
            className="text-gray-600 text-lg max-w-2xl mx-auto"
            variants={itemVariants}
          >
            基于你的心境，我们为你精心挑选了这些独特的生活体验。每一个都能让你在旅行中找到内心的共鸣。
          </motion.p>
        </motion.div>

        {/* 体验卡片网格 */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
          variants={containerVariants}
        >
          {currentLifestyle.experiences.map((experience, index) => (
            <motion.div
              key={experience.id}
              variants={cardVariants}
              whileHover="hover"
              whileTap={{ scale: 0.98 }}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer"
              onClick={() => setSelectedExperience(experience.id)}
              initial="hidden"
              animate="visible"
              transition={{ delay: index * 0.1 }}
            >
              <div className="p-6">
                <motion.div 
                  className="flex items-center justify-center mb-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="text-6xl mr-3">{experience.image}</div>
                  <FontAwesomeIcon 
                    icon={experience.icon as any} 
                    className="text-3xl text-indigo-600 opacity-70"
                  />
                </motion.div>
                
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {experience.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-3 flex items-center">
                  <FontAwesomeIcon icon="map-marker-alt" className="mr-2 text-red-500" />
                  {experience.location}
                </p>
                
                <p className="text-gray-700 mb-4 leading-relaxed">
                  {experience.description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {experience.highlights.map((highlight, index) => (
                    <motion.span
                      key={index}
                      className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm flex items-center"
                      whileHover={{ scale: 1.05 }}
                    >
                      <FontAwesomeIcon icon="check-circle" className="mr-1 text-green-500 text-xs" />
                      {highlight}
                    </motion.span>
                  ))}
                </div>
                
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-2xl font-bold text-indigo-600 flex items-center">
                      <FontAwesomeIcon icon="gem" className="mr-2 text-lg" />
                      {experience.price}
                    </div>
                    <div className="text-gray-500 text-sm flex items-center">
                      <FontAwesomeIcon icon="clock" className="mr-1" />
                      {experience.duration}
                    </div>
                  </div>
                  <motion.button 
                    className="px-6 py-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors flex items-center"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FontAwesomeIcon icon="eye" className="mr-2" />
                    了解详情
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* 底部操作区域 */}
        <motion.div className="text-center" variants={itemVariants}>
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-full font-medium hover:border-indigo-400 hover:text-indigo-600 transition-all duration-300 mr-4"
          >
            <FontAwesomeIcon icon="chevron-left" className="mr-2" />
            重新选择心境
          </Link>
          <motion.button 
            className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FontAwesomeIcon icon="users" className="mr-2" />
            找到志同道合的伙伴
          </motion.button>
        </motion.div>
      </motion.div>
    </div>
  );
}
