{"version": 3, "sources": ["../../src/diagnostics/build-diagnostics.test.ts"], "sourcesContent": ["import { mkdtemp, readFile } from 'fs/promises'\nimport { tmpdir } from 'os'\nimport { join } from 'path'\nimport { setGlobal } from '../trace/shared'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n} from './build-diagnostics'\n\nasync function readBuildDiagnostics(dir: string) {\n  return JSON.parse(\n    await readFile(join(dir, 'diagnostics', 'build-diagnostics.json'), 'utf8')\n  )\n}\n\ndescribe('build-diagnostics', () => {\n  it('records framework version to framework.json correctly', async () => {\n    const tmpDir = await mkdtemp(join(tmpdir(), 'build-diagnostics'))\n    setGlobal('distDir', tmpDir)\n\n    // Record the initial diagnostics and make sure it's correct.\n    await recordFrameworkVersion('14.2.3')\n    let diagnostics = JSON.parse(\n      await readFile(join(tmpDir, 'diagnostics', 'framework.json'), 'utf8')\n    )\n    expect(diagnostics.version).toEqual('14.2.3')\n  })\n\n  it('records build diagnostics to a file correctly', async () => {\n    const tmpDir = await mkdtemp(join(tmpdir(), 'build-diagnostics'))\n    setGlobal('distDir', tmpDir)\n\n    // Record the initial diagnostics and make sure it's correct.\n    await updateBuildDiagnostics({\n      buildStage: 'compile',\n    })\n    let diagnostics = await readBuildDiagnostics(tmpDir)\n    expect(diagnostics.buildStage).toEqual('compile')\n\n    // Add a new build option. Make sure that existing fields are preserved.\n    await updateBuildDiagnostics({\n      buildStage: 'compile-server',\n      buildOptions: {\n        useBuildWorker: String(false),\n      },\n    })\n    diagnostics = await readBuildDiagnostics(tmpDir)\n    expect(diagnostics.buildStage).toEqual('compile-server')\n    expect(diagnostics.buildOptions).toEqual({\n      useBuildWorker: 'false',\n    })\n\n    // Make sure that it keeps existing build options when adding a new one.\n    await updateBuildDiagnostics({\n      buildStage: 'compile-client',\n      buildOptions: {\n        experimentalBuildMode: 'compile',\n      },\n    })\n    diagnostics = await readBuildDiagnostics(tmpDir)\n    expect(diagnostics.buildStage).toEqual('compile-client')\n    expect(diagnostics.buildOptions).toEqual({\n      experimentalBuildMode: 'compile',\n      useBuildWorker: 'false',\n    })\n  })\n})\n"], "names": ["readBuildDiagnostics", "dir", "JSON", "parse", "readFile", "join", "describe", "it", "tmpDir", "mkdtemp", "tmpdir", "setGlobal", "recordFrameworkVersion", "diagnostics", "expect", "version", "toEqual", "updateBuildDiagnostics", "buildStage", "buildOptions", "useBuildWorker", "String", "experimentalBuildMode"], "mappings": ";;;;0BAAkC;oBACX;sBACF;wBACK;kCAInB;AAEP,eAAeA,qBAAqBC,GAAW;IAC7C,OAAOC,KAAKC,KAAK,CACf,MAAMC,IAAAA,kBAAQ,EAACC,IAAAA,UAAI,EAACJ,KAAK,eAAe,2BAA2B;AAEvE;AAEAK,SAAS,qBAAqB;IAC5BC,GAAG,yDAAyD;QAC1D,MAAMC,SAAS,MAAMC,IAAAA,iBAAO,EAACJ,IAAAA,UAAI,EAACK,IAAAA,UAAM,KAAI;QAC5CC,IAAAA,iBAAS,EAAC,WAAWH;QAErB,6DAA6D;QAC7D,MAAMI,IAAAA,wCAAsB,EAAC;QAC7B,IAAIC,cAAcX,KAAKC,KAAK,CAC1B,MAAMC,IAAAA,kBAAQ,EAACC,IAAAA,UAAI,EAACG,QAAQ,eAAe,mBAAmB;QAEhEM,OAAOD,YAAYE,OAAO,EAAEC,OAAO,CAAC;IACtC;IAEAT,GAAG,iDAAiD;QAClD,MAAMC,SAAS,MAAMC,IAAAA,iBAAO,EAACJ,IAAAA,UAAI,EAACK,IAAAA,UAAM,KAAI;QAC5CC,IAAAA,iBAAS,EAAC,WAAWH;QAErB,6DAA6D;QAC7D,MAAMS,IAAAA,wCAAsB,EAAC;YAC3BC,YAAY;QACd;QACA,IAAIL,cAAc,MAAMb,qBAAqBQ;QAC7CM,OAAOD,YAAYK,UAAU,EAAEF,OAAO,CAAC;QAEvC,wEAAwE;QACxE,MAAMC,IAAAA,wCAAsB,EAAC;YAC3BC,YAAY;YACZC,cAAc;gBACZC,gBAAgBC,OAAO;YACzB;QACF;QACAR,cAAc,MAAMb,qBAAqBQ;QACzCM,OAAOD,YAAYK,UAAU,EAAEF,OAAO,CAAC;QACvCF,OAAOD,YAAYM,YAAY,EAAEH,OAAO,CAAC;YACvCI,gBAAgB;QAClB;QAEA,wEAAwE;QACxE,MAAMH,IAAAA,wCAAsB,EAAC;YAC3BC,YAAY;YACZC,cAAc;gBACZG,uBAAuB;YACzB;QACF;QACAT,cAAc,MAAMb,qBAAqBQ;QACzCM,OAAOD,YAAYK,UAAU,EAAEF,OAAO,CAAC;QACvCF,OAAOD,YAAYM,YAAY,EAAEH,OAAO,CAAC;YACvCM,uBAAuB;YACvBF,gBAAgB;QAClB;IACF;AACF", "ignoreList": [0]}