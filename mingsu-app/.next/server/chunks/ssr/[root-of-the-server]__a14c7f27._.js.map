{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/mingsu/mingsu-app/src/app/lifestyle/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useSearchParams } from 'next/navigation';\nimport { useState } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport '../lib/fontawesome';\n\nconst lifestyleData = {\n  peaceful: {\n    title: '慢生活体验者',\n    subtitle: '寻求内心平静的你，值得拥有这些美好体验',\n    color: 'from-blue-400 to-cyan-300',\n    icon: 'spa',\n    experiences: [\n      {\n        id: 1,\n        title: '山间茶舍，学习茶道冥想',\n        location: '杭州西湖',\n        image: '🍵',\n        icon: 'leaf',\n        description: '在云雾缭绕的山间，跟随茶艺师学习正宗茶道，在茶香中找到内心的宁静',\n        price: '¥680/人',\n        duration: '2天1夜',\n        highlights: ['茶道体验', '冥想指导', '山景住宿']\n      },\n      {\n        id: 2,\n        title: '海边瑜伽，日出时的宁静',\n        location: '三亚海棠湾',\n        image: '🧘‍♀️',\n        icon: 'spa',\n        description: '在海浪声中醒来，跟随瑜伽导师在沙滩上迎接第一缕阳光',\n        price: '¥880/人',\n        duration: '3天2夜',\n        highlights: ['海边瑜伽', '日出冥想', '海景别墅']\n      },\n      {\n        id: 3,\n        title: '乡村手工，制作属于你的陶器',\n        location: '景德镇古窑',\n        image: '🏺',\n        icon: 'palette',\n        description: '用双手感受泥土的温度，在陶艺师的指导下创作独一无二的作品',\n        price: '¥580/人',\n        duration: '2天1夜',\n        highlights: ['陶艺制作', '古窑参观', '乡村民宿']\n      }\n    ]\n  },\n  creative: {\n    title: '文艺青年',\n    subtitle: '释放创作灵感，在艺术中找到真实的自己',\n    color: 'from-purple-400 to-pink-300',\n    icon: 'palette',\n    experiences: [\n      {\n        id: 4,\n        title: '书店民宿，与作家深度对话',\n        location: '北京胡同',\n        image: '📚',\n        icon: 'book',\n        description: '住在书香四溢的独立书店里，与驻店作家畅谈文学与人生',\n        price: '¥780/人',\n        duration: '2天1夜',\n        highlights: ['文学沙龙', '作家对话', '胡同文化']\n      },\n      {\n        id: 5,\n        title: '艺术工作室，创作你的第一幅画',\n        location: '大理古城',\n        image: '🎨',\n        icon: 'paint-brush',\n        description: '在苍山洱海的灵感中，跟随艺术家学习绘画技巧，创作属于你的作品',\n        price: '¥980/人',\n        duration: '3天2夜',\n        highlights: ['绘画教学', '艺术创作', '古城漫步']\n      },\n      {\n        id: 6,\n        title: '咖啡烘焙，学习拉花艺术',\n        location: '上海法租界',\n        image: '☕',\n        icon: 'coffee',\n        description: '在百年咖啡馆里学习从烘焙到拉花的全套技艺，品味生活的精致',\n        price: '¥680/人',\n        duration: '1天',\n        highlights: ['咖啡烘焙', '拉花技艺', '法租界文化']\n      }\n    ]\n  },\n  adventure: {\n    title: '探险家模式',\n    subtitle: '挑战自我极限，在冒险中发现更强大的自己',\n    color: 'from-orange-400 to-red-300',\n    icon: 'mountain',\n    experiences: [\n      {\n        id: 7,\n        title: '徒步穿越，征服神秘峡谷',\n        location: '张家界天门山',\n        image: '🥾',\n        icon: 'walking',\n        description: '跟随专业向导穿越原始峡谷，在挑战中突破自我界限',\n        price: '¥1280/人',\n        duration: '3天2夜',\n        highlights: ['峡谷穿越', '野外生存', '山顶露营']\n      },\n      {\n        id: 8,\n        title: '攀岩体验，挑战垂直极限',\n        location: '阳朔遇龙河',\n        image: '🧗‍♂️',\n        icon: 'mountain',\n        description: '在喀斯特地貌的天然岩壁上挑战自我，体验征服高峰的成就感',\n        price: '¥980/人',\n        duration: '2天1夜',\n        highlights: ['攀岩教学', '安全保障', '山水美景']\n      }\n    ]\n  },\n  nature: {\n    title: '自然探索者',\n    subtitle: '回归自然怀抱，与大自然建立深度连接',\n    color: 'from-green-400 to-emerald-300',\n    icon: 'leaf',\n    experiences: [\n      {\n        id: 9,\n        title: '森林木屋，与鸟儿共眠',\n        location: '长白山原始森林',\n        image: '🌲',\n        icon: 'tree',\n        description: '住在树梢上的木屋里，听着鸟鸣入睡，在自然中找到内心的平静',\n        price: '¥880/人',\n        duration: '2天1夜',\n        highlights: ['树屋住宿', '森林徒步', '野生动物观察']\n      },\n      {\n        id: 10,\n        title: '星空露营，与宇宙对话',\n        location: '内蒙古草原',\n        image: '⭐',\n        icon: 'star',\n        description: '在无光污染的草原上仰望星空，感受宇宙的浩瀚与神秘',\n        price: '¥680/人',\n        duration: '2天1夜',\n        highlights: ['星空观测', '草原骑马', '篝火晚会']\n      }\n    ]\n  },\n  connection: {\n    title: '温暖连接者',\n    subtitle: '寻找温暖陪伴，在真挚的人际关系中感受爱与被爱',\n    color: 'from-pink-400 to-rose-300',\n    icon: 'heart',\n    experiences: [\n      {\n        id: 11,\n        title: '家庭农场，体验田园生活',\n        location: '成都郊外',\n        image: '🏡',\n        icon: 'home',\n        description: '与热情的农场主一家共同生活，体验最纯朴的人情味',\n        price: '¥480/人',\n        duration: '2天1夜',\n        highlights: ['农场体验', '家庭聚餐', '乡村生活']\n      },\n      {\n        id: 12,\n        title: '温泉民宿，治愈系温暖',\n        location: '日式温泉小镇',\n        image: '♨️',\n        icon: 'spa',\n        description: '在温泉的温暖中放松身心，与民宿主人分享生活的美好',\n        price: '¥780/人',\n        duration: '2天1夜',\n        highlights: ['私汤温泉', '和式住宿', '温暖服务']\n      }\n    ]\n  }\n};\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      delayChildren: 0.2,\n      staggerChildren: 0.1\n    }\n  }\n};\n\nconst itemVariants = {\n  hidden: { y: 20, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      type: \"spring\",\n      stiffness: 100\n    }\n  }\n};\n\nconst cardVariants = {\n  hidden: { scale: 0.9, opacity: 0 },\n  visible: {\n    scale: 1,\n    opacity: 1,\n    transition: {\n      type: \"spring\",\n      stiffness: 100,\n      damping: 15\n    }\n  },\n  hover: {\n    scale: 1.03,\n    y: -5,\n    transition: {\n      type: \"spring\",\n      stiffness: 400,\n      damping: 10\n    }\n  }\n};\n\nexport default function LifestylePage() {\n  const searchParams = useSearchParams();\n  const mood = searchParams.get('mood') || 'peaceful';\n  const [selectedExperience, setSelectedExperience] = useState<number | null>(null);\n  \n  const currentLifestyle = lifestyleData[mood as keyof typeof lifestyleData];\n\n  if (!currentLifestyle) {\n    return <div>页面未找到</div>;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-white\">\n      {/* 头部导航 */}\n      <nav className=\"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-indigo-600 flex items-center\">\n              <FontAwesomeIcon icon=\"home\" className=\"mr-2\" />\n              民宿 ✨\n            </Link>\n            <div className=\"flex items-center gap-4\">\n              <motion.button \n                className=\"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors flex items-center\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <FontAwesomeIcon icon=\"robot\" className=\"mr-1\" />\n                小宿\n              </motion.button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主内容区域 */}\n      <motion.div \n        className=\"max-w-7xl mx-auto px-6 py-12\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        {/* 标题区域 */}\n        <motion.div className=\"text-center mb-12\" variants={itemVariants}>\n          <motion.div \n            className={`inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r ${currentLifestyle.color} text-white font-semibold text-lg mb-4`}\n            whileHover={{ scale: 1.05 }}\n          >\n            <FontAwesomeIcon icon={currentLifestyle.icon as any} className=\"mr-2\" />\n            {currentLifestyle.title}\n          </motion.div>\n          <motion.h1 \n            className=\"text-3xl md:text-4xl font-bold text-gray-800 mb-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            {currentLifestyle.subtitle}\n          </motion.h1>\n          <motion.p \n            className=\"text-gray-600 text-lg max-w-2xl mx-auto\"\n            variants={itemVariants}\n          >\n            基于你的心境，我们为你精心挑选了这些独特的生活体验。每一个都能让你在旅行中找到内心的共鸣。\n          </motion.p>\n        </motion.div>\n\n        {/* 体验卡片网格 */}\n        <motion.div \n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\"\n          variants={containerVariants}\n        >\n          {currentLifestyle.experiences.map((experience, index) => (\n            <motion.div\n              key={experience.id}\n              variants={cardVariants}\n              whileHover=\"hover\"\n              whileTap={{ scale: 0.98 }}\n              className=\"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer\"\n              onClick={() => setSelectedExperience(experience.id)}\n              initial=\"hidden\"\n              animate=\"visible\"\n              transition={{ delay: index * 0.1 }}\n            >\n              <div className=\"p-6\">\n                <motion.div \n                  className=\"flex items-center justify-center mb-4\"\n                  whileHover={{ scale: 1.1, rotate: 5 }}\n                  transition={{ type: \"spring\", stiffness: 300 }}\n                >\n                  <div className=\"text-6xl mr-3\">{experience.image}</div>\n                  <FontAwesomeIcon \n                    icon={experience.icon as any} \n                    className=\"text-3xl text-indigo-600 opacity-70\"\n                  />\n                </motion.div>\n                \n                <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">\n                  {experience.title}\n                </h3>\n                \n                <p className=\"text-gray-600 text-sm mb-3 flex items-center\">\n                  <FontAwesomeIcon icon=\"map-marker-alt\" className=\"mr-2 text-red-500\" />\n                  {experience.location}\n                </p>\n                \n                <p className=\"text-gray-700 mb-4 leading-relaxed\">\n                  {experience.description}\n                </p>\n                \n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {experience.highlights.map((highlight, index) => (\n                    <motion.span\n                      key={index}\n                      className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm flex items-center\"\n                      whileHover={{ scale: 1.05 }}\n                    >\n                      <FontAwesomeIcon icon=\"check-circle\" className=\"mr-1 text-green-500 text-xs\" />\n                      {highlight}\n                    </motion.span>\n                  ))}\n                </div>\n                \n                <div className=\"flex justify-between items-center\">\n                  <div>\n                    <div className=\"text-2xl font-bold text-indigo-600 flex items-center\">\n                      <FontAwesomeIcon icon=\"gem\" className=\"mr-2 text-lg\" />\n                      {experience.price}\n                    </div>\n                    <div className=\"text-gray-500 text-sm flex items-center\">\n                      <FontAwesomeIcon icon=\"clock\" className=\"mr-1\" />\n                      {experience.duration}\n                    </div>\n                  </div>\n                  <motion.button \n                    className=\"px-6 py-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors flex items-center\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <FontAwesomeIcon icon=\"eye\" className=\"mr-2\" />\n                    了解详情\n                  </motion.button>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* 底部操作区域 */}\n        <motion.div className=\"text-center\" variants={itemVariants}>\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-full font-medium hover:border-indigo-400 hover:text-indigo-600 transition-all duration-300 mr-4\"\n          >\n            <FontAwesomeIcon icon=\"chevron-left\" className=\"mr-2\" />\n            重新选择心境\n          </Link>\n          <motion.button \n            className=\"inline-flex items-center px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <FontAwesomeIcon icon=\"users\" className=\"mr-2\" />\n            找到志同道合的伙伴\n          </motion.button>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;;;;AANA;;;;;;;;AASA,MAAM,gBAAgB;IACpB,UAAU;QACR,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;YACX;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;SACD;IACH;IACA,UAAU;QACR,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;YACX;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAQ;YACvC;SACD;IACH;IACA,WAAW;QACT,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;YACX;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;SACD;IACH;IACA,QAAQ;QACN,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;YACX;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAS;YACxC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;SACD;IACH;IACA,YAAY;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;YACX;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,YAAY;oBAAC;oBAAQ;oBAAQ;iBAAO;YACtC;SACD;IACH;AACF;AAEA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,eAAe;YACf,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,SAAS;QACP,GAAG;QACH,SAAS;QACT,YAAY;YACV,MAAM;YACN,WAAW;QACb;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,OAAO;QAAK,SAAS;IAAE;IACjC,SAAS;QACP,OAAO;QACP,SAAS;QACT,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,OAAO;QACL,OAAO;QACP,GAAG,CAAC;QACJ,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;AACF;AAEe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5E,MAAM,mBAAmB,aAAa,CAAC,KAAmC;IAE1E,IAAI,CAAC,kBAAkB;QACrB,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAK;wCAAO,WAAU;;;;;;oCAAS;;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAK;4CAAQ,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;kCAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;wBAAoB,UAAU;;0CAClD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAW,CAAC,iEAAiE,EAAE,iBAAiB,KAAK,CAAC,sCAAsC,CAAC;gCAC7I,YAAY;oCAAE,OAAO;gCAAK;;kDAE1B,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,iBAAiB,IAAI;wCAAS,WAAU;;;;;;oCAC9D,iBAAiB,KAAK;;;;;;;0CAEzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAExB,iBAAiB,QAAQ;;;;;;0CAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,UAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;kCAET,iBAAiB,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBAC7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,YAAW;gCACX,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;gCACV,SAAS,IAAM,sBAAsB,WAAW,EAAE;gCAClD,SAAQ;gCACR,SAAQ;gCACR,YAAY;oCAAE,OAAO,QAAQ;gCAAI;0CAEjC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;gDAAK,QAAQ;4CAAE;4CACpC,YAAY;gDAAE,MAAM;gDAAU,WAAW;4CAAI;;8DAE7C,8OAAC;oDAAI,WAAU;8DAAiB,WAAW,KAAK;;;;;;8DAChD,8OAAC,oKAAA,CAAA,kBAAe;oDACd,MAAM,WAAW,IAAI;oDACrB,WAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAG,WAAU;sDACX,WAAW,KAAK;;;;;;sDAGnB,8OAAC;4CAAE,WAAU;;8DACX,8OAAC,oKAAA,CAAA,kBAAe;oDAAC,MAAK;oDAAiB,WAAU;;;;;;gDAChD,WAAW,QAAQ;;;;;;;sDAGtB,8OAAC;4CAAE,WAAU;sDACV,WAAW,WAAW;;;;;;sDAGzB,8OAAC;4CAAI,WAAU;sDACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDAEV,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;;sEAE1B,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAK;4DAAe,WAAU;;;;;;wDAC9C;;mDALI;;;;;;;;;;sDAUX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oKAAA,CAAA,kBAAe;oEAAC,MAAK;oEAAM,WAAU;;;;;;gEACrC,WAAW,KAAK;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oKAAA,CAAA,kBAAe;oEAAC,MAAK;oEAAQ,WAAU;;;;;;gEACvC,WAAW,QAAQ;;;;;;;;;;;;;8DAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;;sEAExB,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAK;4DAAM,WAAU;;;;;;wDAAS;;;;;;;;;;;;;;;;;;;+BAjEhD,WAAW,EAAE;;;;;;;;;;kCA2ExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;wBAAc,UAAU;;0CAC5C,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAK;wCAAe,WAAU;;;;;;oCAAS;;;;;;;0CAG1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAK;wCAAQ,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;AAO7D", "debugId": null}}]}